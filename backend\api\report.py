from flask import Blueprint, request, jsonify
from .utils import get_db_connection, format_response
from .auth import token_required, check_permission
from datetime import datetime, timedelta
import json

report_bp = Blueprint('report', __name__, url_prefix='/api')

def get_date_range_params():
    """获取日期范围参数，如果没有提供则返回None表示查询全部"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # 如果没有提供日期范围，返回None表示查询全部数据
    if not start_date or not end_date:
        return None, None

    return start_date, end_date

def build_where_conditions(start_date, end_date):
    """构建通用的WHERE条件"""
    where_conditions = ['is_deleted = 0']
    params = []

    # 日期范围（只有当提供了日期参数时才添加日期条件）
    if start_date and end_date:
        where_conditions.append('record_date >= %s AND record_date <= %s')
        params.extend([start_date, end_date])

    # 其他筛选条件
    shop = request.args.get('shop')
    type_param = request.args.get('type')
    category = request.args.get('category')

    if shop:
        where_conditions.append('shop = %s')
        params.append(shop)

    if type_param:
        where_conditions.append('type = %s')
        params.append(type_param)

    if category:
        where_conditions.append('category = %s')
        params.append(category)

    return where_conditions, params

@report_bp.route('/finance/report/statistics', methods=['GET'])
@token_required
@check_permission('MENU_FINANCE')
def get_finance_statistics():
    """获取财务统计数据"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取当前用户ID
        from flask import g
        from models.role import Role
        current_user_id = g.current_user.get('user_id')

        # 获取用户数据范围
        role_model = Role()
        user_data_scopes = role_model.get_user_data_scopes(current_user_id)
        accessible_store_names = [scope['store_name'] for scope in user_data_scopes if scope['store_type'] == 'offline' and scope['store_name']]

        start_date, end_date = get_date_range_params()
        where_conditions, params = build_where_conditions(start_date, end_date)

        # 数据范围过滤：如果用户有数据范围限制，只显示可访问店铺的数据
        if accessible_store_names:
            placeholders = ','.join(['%s'] * len(accessible_store_names))
            where_conditions.append(f'shop IN ({placeholders})')
            params.extend(accessible_store_names)
        elif user_data_scopes is not None and len(user_data_scopes) == 0:
            # 如果用户有角色但没有数据范围，不显示任何数据
            where_conditions.append('1 = 0')  # 永远为假的条件
        
        # 统计查询SQL
        sql = f"""
        SELECT
            SUM(CASE WHEN type = '收入' THEN amount ELSE 0 END) as total_income,
            SUM(CASE WHEN type = '支出' THEN amount ELSE 0 END) as total_expenditure,
            COUNT(CASE WHEN type = '收入' THEN 1 END) as income_count,
            COUNT(CASE WHEN type = '支出' THEN 1 END) as expenditure_count,
            SUM(CASE WHEN type = '支出' AND is_reimbursed = 1 AND reimbursed_status = 1 THEN amount ELSE 0 END) as reimbursed_amount,
            SUM(CASE WHEN type = '支出' AND is_reimbursed = 1 AND reimbursed_status = 0 THEN amount ELSE 0 END) as unreimbursed_amount,
            COUNT(CASE WHEN type = '支出' AND is_reimbursed = 1 AND reimbursed_status = 1 THEN 1 END) as reimbursed_count,
            COUNT(CASE WHEN type = '支出' AND is_reimbursed = 1 AND reimbursed_status = 0 THEN 1 END) as unreimbursed_count
        FROM finance
        WHERE {' AND '.join(where_conditions)}
        """

        cursor.execute(sql, params)
        result = cursor.fetchone()

        # 查询回款总额（从financepayment表）
        payment_sql = f"""
        SELECT SUM(amount) as total_payment_amount
        FROM financepayment
        WHERE is_deleted = 0
        """

        # 如果有日期筛选条件，也应用到回款查询
        if start_date and end_date:
            payment_sql += " AND record_date >= %s AND record_date <= %s"
            cursor.execute(payment_sql, [start_date, end_date])
        else:
            cursor.execute(payment_sql)

        payment_result = cursor.fetchone()
        total_payment_amount = float(payment_result[0] or 0)
        
        # 如果没有指定日期范围，获取实际数据的日期范围
        actual_start_date = start_date
        actual_end_date = end_date

        if not start_date or not end_date:
            # 查询实际数据的日期范围
            date_range_sql = f"""
            SELECT MIN(record_date), MAX(record_date)
            FROM finance
            WHERE {' AND '.join(where_conditions)}
            """
            cursor.execute(date_range_sql, params)
            date_range_result = cursor.fetchone()
            if date_range_result and date_range_result[0] and date_range_result[1]:
                actual_start_date = date_range_result[0].strftime('%Y-%m-%d') if hasattr(date_range_result[0], 'strftime') else str(date_range_result[0])
                actual_end_date = date_range_result[1].strftime('%Y-%m-%d') if hasattr(date_range_result[1], 'strftime') else str(date_range_result[1])
            else:
                # 如果没有数据，使用当前日期
                actual_start_date = datetime.now().strftime('%Y-%m-%d')
                actual_end_date = datetime.now().strftime('%Y-%m-%d')

        # 格式化结果，保留两位小数
        statistics = {
            'totalIncome': round(float(result[0] or 0), 2),
            'totalExpenditure': round(float(result[1] or 0), 2),
            'incomeCount': int(result[2] or 0),
            'expenditureCount': int(result[3] or 0),
            'reimbursedAmount': round(float(result[4] or 0), 2),
            'unreimbursedAmount': round(float(result[5] or 0), 2),
            'reimbursedCount': int(result[6] or 0),
            'unreimbursedCount': int(result[7] or 0),
            'netIncome': round(float(result[0] or 0) - float(result[1] or 0), 2),
            'totalPaymentAmount': round(total_payment_amount, 2),  # 回款总额
            'dateRange': {
                'startDate': actual_start_date,
                'endDate': actual_end_date
            }
        }
        
        return format_response(
            success=True,
            data=statistics,
            message="获取统计数据成功"
        )
        
    except Exception as e:
        print(f"获取统计数据错误: {str(e)}")
        return format_response(success=False, message="获取统计数据失败")
    finally:
        conn.close()

@report_bp.route('/finance/report/distribution', methods=['GET'])
@token_required
@check_permission('MENU_FINANCE')
def get_finance_distribution():
    """获取财务分布数据（用于饼图）"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取当前用户ID
        from flask import g
        from models.role import Role
        current_user_id = g.current_user.get('user_id')

        # 获取用户数据范围
        role_model = Role()
        user_data_scopes = role_model.get_user_data_scopes(current_user_id)
        accessible_store_names = [scope['store_name'] for scope in user_data_scopes if scope['store_type'] == 'offline' and scope['store_name']]

        start_date, end_date = get_date_range_params()
        where_conditions, params = build_where_conditions(start_date, end_date)

        # 数据范围过滤：如果用户有数据范围限制，只显示可访问店铺的数据
        if accessible_store_names:
            placeholders = ','.join(['%s'] * len(accessible_store_names))
            where_conditions.append(f'shop IN ({placeholders})')
            params.extend(accessible_store_names)
        elif user_data_scopes is not None and len(user_data_scopes) == 0:
            # 如果用户有角色但没有数据范围，不显示任何数据
            where_conditions.append('1 = 0')  # 永远为假的条件
        
        # 收入支出分布
        income_expense_sql = f"""
        SELECT 
            type,
            SUM(amount) as total_amount,
            COUNT(*) as count
        FROM finance 
        WHERE {' AND '.join(where_conditions)}
        GROUP BY type
        """
        
        cursor.execute(income_expense_sql, params)
        income_expense_data = [
            {
                'name': row[0],
                'value': round(float(row[1]), 2),
                'count': int(row[2])
            }
            for row in cursor.fetchall()
        ]
        
        # 收入分类分布
        income_category_sql = f"""
        SELECT 
            category,
            SUM(amount) as total_amount,
            COUNT(*) as count
        FROM finance 
        WHERE {' AND '.join(where_conditions)} AND type = '收入'
        GROUP BY category
        ORDER BY total_amount DESC
        """
        
        cursor.execute(income_category_sql, params)
        income_category_data = [
            {
                'name': row[0],
                'value': round(float(row[1]), 2),
                'count': int(row[2])
            }
            for row in cursor.fetchall()
        ]
        
        # 支出分类分布
        expense_category_sql = f"""
        SELECT 
            category,
            SUM(amount) as total_amount,
            COUNT(*) as count
        FROM finance 
        WHERE {' AND '.join(where_conditions)} AND type = '支出'
        GROUP BY category
        ORDER BY total_amount DESC
        """
        
        cursor.execute(expense_category_sql, params)
        expense_category_data = [
            {
                'name': row[0],
                'value': round(float(row[1]), 2),
                'count': int(row[2])
            }
            for row in cursor.fetchall()
        ]

        # 净利润分布（按店铺）
        # 首先获取每个店铺的收入和支出
        shop_finance_sql = f"""
        SELECT
            shop,
            SUM(CASE WHEN type = '收入' THEN amount ELSE 0 END) as total_income,
            SUM(CASE WHEN type = '支出' THEN amount ELSE 0 END) as total_expenditure
        FROM finance
        WHERE {' AND '.join(where_conditions)}
        GROUP BY shop
        """

        cursor.execute(shop_finance_sql, params)
        shop_finance_results = cursor.fetchall()

        # 获取每个店铺的回款金额
        payment_where_conditions = ['is_deleted = 0']
        payment_params = []

        if start_date and end_date:
            payment_where_conditions.append('record_date >= %s AND record_date <= %s')
            payment_params.extend([start_date, end_date])

        shop_payment_sql = f"""
        SELECT
            shop,
            SUM(amount) as total_payment
        FROM financepayment
        WHERE {' AND '.join(payment_where_conditions)}
        GROUP BY shop
        """

        cursor.execute(shop_payment_sql, payment_params)
        shop_payment_results = {row[0]: float(row[1] or 0) for row in cursor.fetchall()}

        # 计算每个店铺的净利润
        net_profit_data = []
        for row in shop_finance_results:
            shop = row[0]
            total_income = float(row[1] or 0)
            total_expenditure = float(row[2] or 0)
            total_payment = shop_payment_results.get(shop, 0)

            # 净利润 = 回款总额 - 总支出
            net_profit = total_payment - total_expenditure

            if net_profit != 0:  # 只显示有净利润的店铺
                net_profit_data.append({
                    'name': shop,
                    'value': round(net_profit, 2),
                    'count': 1  # 店铺数量为1
                })

        # 按净利润降序排列
        net_profit_data.sort(key=lambda x: x['value'], reverse=True)

        # 如果没有指定日期范围，获取实际数据的日期范围
        actual_start_date = start_date
        actual_end_date = end_date

        if not start_date or not end_date:
            # 查询实际数据的日期范围
            date_range_sql = f"""
            SELECT MIN(record_date), MAX(record_date)
            FROM finance
            WHERE {' AND '.join(where_conditions)}
            """
            cursor.execute(date_range_sql, params)
            date_range_result = cursor.fetchone()
            if date_range_result and date_range_result[0] and date_range_result[1]:
                actual_start_date = date_range_result[0].strftime('%Y-%m-%d') if hasattr(date_range_result[0], 'strftime') else str(date_range_result[0])
                actual_end_date = date_range_result[1].strftime('%Y-%m-%d') if hasattr(date_range_result[1], 'strftime') else str(date_range_result[1])
            else:
                # 如果没有数据，使用当前日期
                actual_start_date = datetime.now().strftime('%Y-%m-%d')
                actual_end_date = datetime.now().strftime('%Y-%m-%d')

        distribution_data = {
            'incomeExpense': income_expense_data,
            'incomeCategory': income_category_data,
            'expenseCategory': expense_category_data,
            'netProfitDistribution': net_profit_data,
            'dateRange': {
                'startDate': actual_start_date,
                'endDate': actual_end_date
            }
        }
        
        return format_response(
            success=True,
            data=distribution_data,
            message="获取分布数据成功"
        )

    except Exception as e:
        print(f"获取分布数据错误: {str(e)}")
        return format_response(success=False, message="获取分布数据失败")
    finally:
        conn.close()

@report_bp.route('/finance/report/shop-daily', methods=['GET'])
@token_required
@check_permission('MENU_FINANCE')
def get_shop_daily_data():
    """获取店铺每日收支数据（用于折线图）"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取当前用户ID
        from flask import g
        from models.role import Role
        current_user_id = g.current_user.get('user_id')

        # 获取用户数据范围
        role_model = Role()
        user_data_scopes = role_model.get_user_data_scopes(current_user_id)
        accessible_store_names = [scope['store_name'] for scope in user_data_scopes if scope['store_type'] == 'offline' and scope['store_name']]

        start_date, end_date = get_date_range_params()
        where_conditions, params = build_where_conditions(start_date, end_date)

        # 数据范围过滤：如果用户有数据范围限制，只显示可访问店铺的数据
        if accessible_store_names:
            placeholders = ','.join(['%s'] * len(accessible_store_names))
            where_conditions.append(f'shop IN ({placeholders})')
            params.extend(accessible_store_names)
        elif user_data_scopes is not None and len(user_data_scopes) == 0:
            # 如果用户有角色但没有数据范围，不显示任何数据
            where_conditions.append('1 = 0')  # 永远为假的条件

        # 每日收支数据
        daily_sql = f"""
        SELECT
            record_date,
            shop,
            SUM(CASE WHEN type = '收入' THEN amount ELSE 0 END) as daily_income,
            SUM(CASE WHEN type = '支出' THEN amount ELSE 0 END) as daily_expenditure
        FROM finance
        WHERE {' AND '.join(where_conditions)}
        GROUP BY record_date, shop
        ORDER BY record_date, shop
        """

        cursor.execute(daily_sql, params)
        daily_results = cursor.fetchall()

        # 组织数据结构
        shop_daily_data = {}
        date_set = set()

        for row in daily_results:
            date, shop, income, expenditure = row
            date_str = date.strftime('%Y-%m-%d') if hasattr(date, 'strftime') else str(date)
            date_set.add(date_str)

            if shop not in shop_daily_data:
                shop_daily_data[shop] = {
                    'shop': shop,
                    'incomeData': {},
                    'expenditureData': {}
                }

            shop_daily_data[shop]['incomeData'][date_str] = round(float(income or 0), 2)
            shop_daily_data[shop]['expenditureData'][date_str] = round(float(expenditure or 0), 2)

        # 确保所有日期都有数据（填充0）
        sorted_dates = sorted(list(date_set))
        for shop_data in shop_daily_data.values():
            for date_str in sorted_dates:
                if date_str not in shop_data['incomeData']:
                    shop_data['incomeData'][date_str] = 0
                if date_str not in shop_data['expenditureData']:
                    shop_data['expenditureData'][date_str] = 0

        # 如果没有指定日期范围，使用实际数据的日期范围
        actual_start_date = start_date
        actual_end_date = end_date

        if not start_date or not end_date:
            if sorted_dates:
                actual_start_date = sorted_dates[0]
                actual_end_date = sorted_dates[-1]
            else:
                # 如果没有数据，使用当前日期
                actual_start_date = datetime.now().strftime('%Y-%m-%d')
                actual_end_date = datetime.now().strftime('%Y-%m-%d')

        # 转换为前端需要的格式
        result_data = {
            'dates': sorted_dates,
            'shops': list(shop_daily_data.values()),
            'dateRange': {
                'startDate': actual_start_date,
                'endDate': actual_end_date
            }
        }

        return format_response(
            success=True,
            data=result_data,
            message="获取店铺每日数据成功"
        )

    except Exception as e:
        print(f"获取店铺每日数据错误: {str(e)}")
        return format_response(success=False, message="获取店铺每日数据失败")
    finally:
        conn.close()

@report_bp.route('/finance/report/shop-detail', methods=['GET'])
@token_required
@check_permission('MENU_FINANCE')
def get_shop_detail_data():
    """获取店铺详细统计数据（用于表格）"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取当前用户ID
        from flask import g
        from models.role import Role
        current_user_id = g.current_user.get('user_id')

        # 获取用户数据范围
        role_model = Role()
        user_data_scopes = role_model.get_user_data_scopes(current_user_id)
        accessible_store_names = [scope['store_name'] for scope in user_data_scopes if scope['store_type'] == 'offline' and scope['store_name']]

        start_date, end_date = get_date_range_params()
        where_conditions, params = build_where_conditions(start_date, end_date)

        # 数据范围过滤：如果用户有数据范围限制，只显示可访问店铺的数据
        if accessible_store_names:
            placeholders = ','.join(['%s'] * len(accessible_store_names))
            where_conditions.append(f'shop IN ({placeholders})')
            params.extend(accessible_store_names)
        elif user_data_scopes is not None and len(user_data_scopes) == 0:
            # 如果用户有角色但没有数据范围，不显示任何数据
            where_conditions.append('1 = 0')  # 永远为假的条件

        # 店铺详细统计
        shop_detail_sql = f"""
        SELECT
            shop,
            SUM(CASE WHEN type = '收入' THEN amount ELSE 0 END) as total_income,
            SUM(CASE WHEN type = '支出' THEN amount ELSE 0 END) as total_expenditure,
            COUNT(CASE WHEN type = '收入' THEN 1 END) as income_count,
            COUNT(CASE WHEN type = '支出' THEN 1 END) as expenditure_count,
            COUNT(*) as record_count,
            SUM(CASE WHEN type = '支出' AND is_reimbursed = 1 AND reimbursed_status = 1 THEN amount ELSE 0 END) as reimbursed_amount,
            SUM(CASE WHEN type = '支出' AND is_reimbursed = 1 AND reimbursed_status = 0 THEN amount ELSE 0 END) as unreimbursed_amount,
            COUNT(CASE WHEN type = '支出' AND is_reimbursed = 1 AND reimbursed_status = 1 THEN 1 END) as reimbursed_count,
            COUNT(CASE WHEN type = '支出' AND is_reimbursed = 1 AND reimbursed_status = 0 THEN 1 END) as unreimbursed_count
        FROM finance
        WHERE {' AND '.join(where_conditions)}
        GROUP BY shop
        ORDER BY total_income DESC
        """

        cursor.execute(shop_detail_sql, params)
        shop_results = cursor.fetchall()

        # 获取每个店铺的回款金额
        payment_where_conditions = ['is_deleted = 0']
        payment_params = []

        if start_date and end_date:
            payment_where_conditions.append('record_date >= %s AND record_date <= %s')
            payment_params.extend([start_date, end_date])

        shop_payment_sql = f"""
        SELECT
            shop,
            SUM(amount) as payment_amount
        FROM financepayment
        WHERE {' AND '.join(payment_where_conditions)}
        GROUP BY shop
        """

        cursor.execute(shop_payment_sql, payment_params)
        shop_payment_results = {row[0]: float(row[1] or 0) for row in cursor.fetchall()}

        # 格式化结果，保留两位小数
        shop_detail_data = []
        for row in shop_results:
            shop = row[0]
            payment_amount = shop_payment_results.get(shop, 0)

            shop_detail_data.append({
                'shop': shop,
                'totalIncome': round(float(row[1] or 0), 2),
                'totalExpenditure': round(float(row[2] or 0), 2),
                'incomeCount': int(row[3] or 0),
                'expenditureCount': int(row[4] or 0),
                'recordCount': int(row[5] or 0),
                'reimbursedAmount': round(float(row[6] or 0), 2),
                'unreimbursedAmount': round(float(row[7] or 0), 2),
                'reimbursedCount': int(row[8] or 0),
                'unreimbursedCount': int(row[9] or 0),
                'netIncome': round(float(row[1] or 0) - float(row[2] or 0), 2),
                'paymentAmount': round(payment_amount, 2)  # 回款金额
            })

        # 如果没有指定日期范围，获取实际数据的日期范围
        actual_start_date = start_date
        actual_end_date = end_date

        if not start_date or not end_date:
            # 查询实际数据的日期范围
            date_range_sql = f"""
            SELECT MIN(record_date), MAX(record_date)
            FROM finance
            WHERE {' AND '.join(where_conditions)}
            """
            cursor.execute(date_range_sql, params)
            date_range_result = cursor.fetchone()
            if date_range_result and date_range_result[0] and date_range_result[1]:
                actual_start_date = date_range_result[0].strftime('%Y-%m-%d') if hasattr(date_range_result[0], 'strftime') else str(date_range_result[0])
                actual_end_date = date_range_result[1].strftime('%Y-%m-%d') if hasattr(date_range_result[1], 'strftime') else str(date_range_result[1])
            else:
                # 如果没有数据，使用当前日期
                actual_start_date = datetime.now().strftime('%Y-%m-%d')
                actual_end_date = datetime.now().strftime('%Y-%m-%d')

        result_data = {
            'shopDetails': shop_detail_data,
            'dateRange': {
                'startDate': actual_start_date,
                'endDate': actual_end_date
            }
        }

        return format_response(
            success=True,
            data=result_data,
            message="获取店铺详细数据成功"
        )

    except Exception as e:
        print(f"获取店铺详细数据错误: {str(e)}")
        return format_response(success=False, message="获取店铺详细数据失败")
    finally:
        conn.close()
