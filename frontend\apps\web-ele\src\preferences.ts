import { defineOverridesPreferences } from '@vben/preferences';

/**
 * @description 项目配置文件
 * 只需要覆盖项目中的一部分配置，不需要的配置不用覆盖，会自动使用默认配置
 * !!! 更改配置后请清空缓存，否则可能不生效
 */
export const overridesPreferences = defineOverridesPreferences({
  // overrides
  app: {
    name: import.meta.env.VITE_APP_TITLE,
    defaultHomePath: '/home', // 修复首页404：路由只注册了/home，没有/home/<USER>
    accessMode: 'backend', // 修复菜单重复：使用纯后端模式，避免静态路由和动态路由冲突
    authPageLayout: 'panel-center', // 设置登录页面为居中布局
  },
  logo: {
    enable: true,
    source: '/favicon.svg', // 使用绝对路径引用public目录下的favicon.svg
  },
  theme: {
    mode: 'light', // 设置默认主题为日间模式
  },
});
