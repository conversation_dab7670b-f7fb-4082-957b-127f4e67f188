<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import {
  ElMessage,
  ElDialog,
  ElForm,
  ElFormItem,
  ElRow,
  ElCol,
  ElInput,
  ElInputNumber,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElButton,
  ElCheckbox
} from 'element-plus';
// import { Close } from '@element-plus/icons-vue'; // 如果没有安装图标包，使用文本替代
import type { FormItemRule } from 'element-plus';
import { addFinance, updateFinance, fetchOfflineStores, fetchIncomeCategories, fetchExpenditureCategories } from '../../../../api/finance';
import { deleteImage } from '../../../../api/upload';
import ImageUpload from '../../../../components/image-upload.vue';
import { useDataScope } from '../../../../composables/useDataScope';

// 弹窗可见性
const visible = defineModel<boolean>('visible', { default: false });
// 操作类型：'add' | 'edit'
const props = defineProps<{ operateType: 'add' | 'edit'; rowData?: any }>();
const emit = defineEmits(['submitted']);

// 表单数据
const formRef = ref<any>(null);
const imageUploadRef = ref<any>(null);
const formModel = ref({
  shop: '',           // 店铺
  record_date: '',    // 记账日期
  type: '',           // 类型（收入/支出）
  category: '',       // 分类
  amount: 0,          // 金额
  description: '',    // 描述
  screenshot: '',     // 凭证图片
  needReimbursement: false  // 需要报销（仅支出时显示）
});

// 保存原始数据用于比较（编辑时）
const originalData = ref<any>({});

// 店铺选项
const storeOptions = ref<Array<{ label: string; value: string }>>([]);
// 分类选项
const categoryOptions = ref<Array<{ label: string; value: string }>>([]);

// 数据范围
const { filterAccessibleStoreOptions } = useDataScope();

// 图片处理
const originalImageUrl = ref<string>(''); // 保存原始图片URL

const handleImageFileChange = () => {
  // 不需要特殊处理，ImageUpload组件会直接上传并更新v-model
};

// 收支记录不需要自动计算

// 自定义验证函数
function validateForm() {
  const errors = [];

  if (!formModel.value.shop || formModel.value.shop.trim() === '') {
    errors.push('请选择店铺');
  }

  if (!formModel.value.record_date) {
    errors.push('请选择记账日期');
  }

  if (!formModel.value.type || formModel.value.type.trim() === '') {
    errors.push('请选择类型');
  }

  if (!formModel.value.category || formModel.value.category.trim() === '') {
    errors.push('请选择分类');
  }

  if (!formModel.value.amount || formModel.value.amount <= 0) {
    errors.push('请输入有效金额');
  }
  if (!formModel.value.description || formModel.value.description.trim() === '') {
    errors.push('请输入描述');
  }
  
  if (!formModel.value.screenshot || formModel.value.screenshot.trim() === '') {
    errors.push('请上传凭证图片');
  }
  return errors;
}

// 表单校验规则
const rules = computed<Record<string, FormItemRule[]>>(() => {
  return {
    shop: [
      { required: true, message: '请选择店铺', trigger: ['blur', 'change'] },
      { min: 1, message: '请选择店铺', trigger: ['blur', 'change'] }
    ],
    record_date: [
      { required: true, message: '请选择记账日期', trigger: ['blur', 'change'] }
    ],
    type: [
      { required: true, message: '请选择类型', trigger: ['blur', 'change'] }
    ],
    category: [
      { required: true, message: '请选择分类', trigger: ['blur', 'change'] },
      { min: 1, message: '请选择分类', trigger: ['blur', 'change'] }
    ],
    amount: [
      { required: true, message: '请输入金额', trigger: ['blur', 'change'] },
      { type: 'number', min: 0.01, message: '金额必须大于0', trigger: ['blur', 'change'] }
    ],
    description: [
      { required: false, message: '请输入描述', trigger: ['blur', 'change'] },
      { min: 1, message: '描述不能为空', trigger: ['blur', 'change'] }
    ],
    screenshot: [
      { required: false, message: '请上传凭证图片', trigger: ['blur', 'change'] }
    ]
  };
});

// 弹窗标题
const title = computed(() => (props.operateType === 'add' ? '新增收支记录' : '编辑收支记录'));

// 打开弹窗时初始化表单
watch(visible, (val) => {
  if (val && props.operateType === 'add') {
    // 新增模式：清空表单
    originalData.value = {};
    formModel.value = {
      shop: '',
      record_date: '',
      type: '',
      category: '',
      amount: 0,
      description: '',
      screenshot: '',
      needReimbursement: false
    };
  } else if (!val) {
    // 弹窗关闭时无需特殊处理
  }
});

// 设置编辑数据的函数
async function setEditData(val: any) {
  if (props.operateType === 'edit' && val) {
    // 保存原始数据
    originalData.value = { ...val };
    // 保存原始图片URL，用于后续判断是否需要删除
    originalImageUrl.value = val.screenshot || '';

    formModel.value = {
      shop: val.shop || '',
      record_date: val.record_date || '',
      type: val.type || '',
      category: '', // 先清空分类，等分类选项加载完成后再设置
      amount: val.amount || 0,
      description: val.description || '',
      screenshot: val.screenshot || '',
      needReimbursement: val.is_reimbursed === 1 // 根据数据库字段is_reimbursed设置复选框状态
    };

    // 如果有类型，先加载对应的分类选项，然后设置分类值
    if (val.type) {
      await loadCategoryOptions();
      // 分类选项加载完成后，设置分类值
      formModel.value.category = val.category || '';
    }
  }
}

// watch rowData，编辑时自动回显
watch(
  () => props.rowData,
  setEditData,
  { immediate: true }
);



// 比较数据是否有变化
function hasDataChanged() {
  if (props.operateType === 'add') {
    // 新增模式：检查是否有必填数据
    return formModel.value.shop !== '' || formModel.value.record_date !== '' || formModel.value.type !== '' || formModel.value.category !== '';
  } else {
    // 编辑模式：逐字段比较，处理数据结构差异
    const original = originalData.value;
    const current = formModel.value;

    return (
      current.shop !== (original.shop || '') ||
      current.record_date !== (original.record_date || '') ||
      current.type !== (original.type || '') ||
      current.category !== (original.category || '') ||
      current.amount !== (original.amount || 0) ||
      current.description !== (original.description || '') ||
      current.screenshot !== (original.screenshot || '') ||
      current.needReimbursement !== (original.is_reimbursed === 1) // 比较报销状态
    );
  }
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) {
    ElMessage.error('表单未加载完成，请稍后重试');
    return;
  }

  // 自定义前端验证
  const validationErrors = validateForm();
  if (validationErrors.length > 0) {
    ElMessage.error(validationErrors[0]); // 显示第一个错误
    return;
  }

  // Element Plus 表单验证
  try {
    const isValid = await formRef.value.validate();
    if (!isValid) {
      ElMessage.error('请完善必填信息');
      return;
    }
  } catch (error) {
    // 验证失败
    ElMessage.error('请检查表单填写是否正确');
    console.log('表单验证失败:', error);
    return;
  }

  // 检查数据变化
  if (!hasDataChanged()) {
    ElMessage.warning('数据未发生变化，无需提交');
    visible.value = false;
    return;
  }

  try {

    // 1. 处理图片删除（编辑模式下）
    if (props.operateType === 'edit' && originalImageUrl.value &&
        originalImageUrl.value !== formModel.value.screenshot) {
      // 原始图片被删除了，需要删除物理文件
      try {
        await deleteImage(originalImageUrl.value);
      } catch (error) {
        console.error('删除原始图片失败:', error);
        // 不阻止提交，继续执行
      }
    }

    // 2. 准备提交数据
    const submitData: any = {
      shop: formModel.value.shop,
      record_date: formModel.value.record_date,
      type: formModel.value.type,
      category: formModel.value.category,
      amount: formModel.value.amount,
      description: formModel.value.description,
      screenshot: formModel.value.screenshot,
      needReimbursement: formModel.value.needReimbursement
    };

    // 3. 调用API
    if (props.operateType === 'add') {
      await addFinance(submitData);
    } else {
      await updateFinance(props.rowData?.id, submitData);
    }

    ElMessage.success(props.operateType === 'add' ? '新增成功' : '编辑成功');
    visible.value = false;
    emit('submitted');

  } catch (error: any) {
    // 统一错误处理
    console.error('操作失败:', error);

    if (error?.response?.data?.message) {
      // API错误 - 显示后端返回的错误信息
      ElMessage.error(error.response.data.message);
    } else if (error?.response?.status === 400) {
      // 400错误通常是参数问题
      ElMessage.error('请检查填写的信息是否正确');
    } else if (error?.response?.status === 401) {
      // 401错误是权限问题
      ElMessage.error('登录已过期，请重新登录');
    } else if (error?.response?.status >= 500) {
      // 500错误是服务器问题
      ElMessage.error('服务器错误，请稍后重试');
    } else {
      // 其他错误
      ElMessage.error(props.operateType === 'add' ? '新增失败，请重试' : '编辑失败，请重试');
    }
  }
}

// 加载店铺选项
async function loadStoreOptions() {
  try {
    const response = await fetchOfflineStores();

    if (response && response.data) {
      const allStoreOptions = response.data
        .filter(store => store.status === 1) // 只显示启用的店铺
        .map(store => ({
          label: store.name,
          value: store.name
        }));

      // 应用数据范围过滤
      storeOptions.value = filterAccessibleStoreOptions(allStoreOptions);
    } else {
      storeOptions.value = [];
    }
  } catch (error) {
    console.error('加载店铺选项失败:', error);
    storeOptions.value = [];
  }
}

// 加载分类选项
async function loadCategoryOptions() {
  try {
    const type = formModel.value.type;

    if (!type) {
      categoryOptions.value = [];
      return;
    }

    let response;
    if (type === '收入') {
      response = await fetchIncomeCategories();
    } else if (type === '支出') {
      response = await fetchExpenditureCategories();
    }

    if (response && response.data) {
      categoryOptions.value = response.data
        .filter(category => category.status === 1) // 只显示启用的分类
        .map(category => ({
          label: category.name,
          value: category.name
        }));
    } else {
      categoryOptions.value = [];
    }
  } catch (error) {
    console.error('加载分类选项失败:', error);
    categoryOptions.value = [];
  }
}

// 监听类型变化，重新加载分类选项
watch(() => formModel.value.type, () => {
  // 清空分类选择
  formModel.value.category = '';
  // 重新加载分类选项
  loadCategoryOptions();
});

// 组件挂载时的初始化
onMounted(() => {
  loadStoreOptions();
  loadCategoryOptions();
});
</script>

<template>
  <ElDialog v-model="visible" :title="title" width="600px" @close="visible = false">
    <ElForm ref="formRef" :model="formModel" :rules="rules" label-width="110px">
      <ElRow :gutter="24">
        <!-- 店铺 -->
        <ElCol :span="12">
          <ElFormItem label="店铺" prop="shop">
            <ElSelect v-model="formModel.shop" placeholder="请选择店铺" style="width: 100%" filterable>
              <ElOption
                v-for="option in storeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <!-- 记账日期 -->
        <ElCol :span="12">
          <ElFormItem label="记账日期" prop="record_date">
            <ElDatePicker
              v-model="formModel.record_date"
              type="date"
              placeholder="请选择记账日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </ElFormItem>
        </ElCol>
        <!-- 类型 -->
        <ElCol :span="12">
          <ElFormItem label="类型" prop="type">
            <ElSelect v-model="formModel.type" placeholder="请选择类型" style="width: 100%">
              <ElOption label="收入" value="收入" />
              <ElOption label="支出" value="支出" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <!-- 分类 -->
        <ElCol :span="12">
          <ElFormItem label="分类" prop="category">
            <ElSelect v-model="formModel.category" placeholder="请选择分类" style="width: 100%" filterable>
              <ElOption
                v-for="option in categoryOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <!-- 金额 -->
        <ElCol :span="12">
          <ElFormItem label="金额" prop="amount">
            <ElInputNumber v-model="formModel.amount" :min="0" :precision="2" style="width: 100%" />
          </ElFormItem>
          <!-- 需要报销复选框（仅支出时显示） -->
          <ElFormItem v-if="formModel.type === '支出'" label="" style="margin-top: 10px;">
            <ElCheckbox v-model="formModel.needReimbursement">需要报销</ElCheckbox>
          </ElFormItem>
        </ElCol>
        <!-- 描述 -->
        <ElCol :span="12">
          <ElFormItem label="描述" prop="description">
            <ElInput v-model="formModel.description" placeholder="请输入描述" type="textarea" :rows="3" />
          </ElFormItem>
        </ElCol>
        <!-- 凭证图片 -->
        <ElCol :span="24">
          <ElFormItem label="凭证图片" prop="screenshot">
            <ImageUpload
              ref="imageUploadRef"
              v-model="formModel.screenshot"
              :multiple="false"
              :limit="1"
              accept="image/*"
              list-type="picture-card"
              button-text="上传凭证"
              :edit-mode="props.operateType === 'edit'"
              :required="true"
              @file-change="handleImageFileChange"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <ElButton @click="visible = false">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit">确认</ElButton>
    </template>
  </ElDialog>

</template>

<style scoped>
/* 自动计算提示样式 */
.auto-calc-tip {
  font-size: 12px;
  color: #909399;
  font-weight: normal;
}

.auto-calc-input :deep(.el-input__inner) {
  background-color: #f5f7fa;
  color: #606266;
}

.auto-calc-input :deep(.el-input-number__decrease),
.auto-calc-input :deep(.el-input-number__increase) {
  display: none;
}

/* 参考样式的图片上传组件 */
.avatar-uploader {
  display: flex;
  align-items: center;
}

.img-upload-box {
  position: relative;
  width: 120px;
  height: 120px;
}

.img-upload-plus {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #ccc;
  font-size: 48px;
  color: #aaa;
  cursor: pointer;
}

.img-preview-wrapper {
  position: relative;
  width: 120px;
  height: 120px;
}

.img-action-bar-horizontal-center {
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: row;
  z-index: 2;
}

.img-action-bar-horizontal-center .el-button {
  margin: 0;
  padding: 2px;
  font-size: 12px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}


/* 弹窗样式 */
:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #ebeef5;
}
</style>