import { requestClient } from './request';

/**
 * 角色管理API接口
 */

// 角色信息接口类型定义
export interface RoleInfo {
  id: number;
  name: string;
  description?: string;
  status: number;
  permission_count?: number;
  user_count?: number;
  created_at: string;
  updated_at: string;
}

// 角色列表查询参数
export interface RoleListParams {
  page: number;
  pageSize: number;
  name?: string;
  status?: string;
}

// 角色列表响应数据
export interface RoleListResponse {
  data: RoleInfo[];
  total: number;
  page: number;
  page_size: number;
}

// 新增/编辑角色参数
export interface RoleOperateParams {
  name: string;
  description?: string;
  status?: number;
}

/**
 * 获取角色列表
 * @param params 查询参数
 * @returns 角色列表数据
 */
export async function fetchRoleList(params: RoleListParams) {
  return requestClient.get<RoleListResponse>('/role/list', {
    params
  });
}

/**
 * 新增角色
 * @param data 角色数据
 * @returns 操作结果
 */
export async function addRole(data: RoleOperateParams) {
  return requestClient.post('/role/create', data);
}

/**
 * 更新角色信息
 * @param id 角色ID
 * @param data 角色数据
 * @returns 操作结果
 */
export async function updateRole(id: number, data: RoleOperateParams) {
  return requestClient.put(`/role/update/${id}`, data);
}

/**
 * 删除角色
 * @param id 角色ID
 * @returns 操作结果
 */
export async function deleteRole(id: number) {
  return requestClient.delete(`/role/delete/${id}`);
}

/**
 * 批量删除角色
 * @param ids 角色ID数组
 * @returns 操作结果
 */
export async function batchDeleteRoles(ids: number[]) {
  return requestClient.post('/role/batch-delete', { ids });
}

/**
 * 更新角色状态
 * @param id 角色ID
 * @param status 状态
 * @returns 操作结果
 */
export async function updateRoleStatus(id: number, status: number) {
  return requestClient.put(`/role/status/${id}`, { status });
}

/**
 * 获取所有角色（用于下拉选择）
 * @returns 角色列表
 */
export async function fetchAllRoles() {
  return requestClient.get<RoleInfo[]>('/role/all');
}

/**
 * 获取角色权限
 * @param id 角色ID
 * @returns 权限列表
 */
export async function fetchRolePermissions(id: number) {
  return requestClient.get(`/role/${id}/permissions`);
}

/**
 * 分配角色权限
 * @param id 角色ID
 * @param permissionIds 权限ID数组
 * @returns 操作结果
 */
export async function assignRolePermissions(id: number, permissionIds: number[]) {
  return requestClient.post(`/role/${id}/permissions`, { permission_ids: permissionIds });
}

// 数据范围相关接口类型定义
export interface DataScope {
  store_id: number;
  store_type: 'offline' | 'online';
  store_name?: string;
}

/**
 * 获取角色数据范围
 * @param roleId 角色ID
 * @returns 数据范围列表
 */
export async function fetchRoleDataScopes(roleId: number) {
  return requestClient.get(`/role/${roleId}/data-scopes`);
}

/**
 * 设置角色数据范围
 * @param roleId 角色ID
 * @param storeScopes 店铺范围列表
 * @returns 设置结果
 */
export async function setRoleDataScopes(roleId: number, storeScopes: DataScope[]) {
  return requestClient.post(`/role/${roleId}/data-scopes`, {
    store_scopes: storeScopes
  });
}

/**
 * 获取用户数据范围
 * @param userId 用户ID
 * @returns 数据范围列表
 */
export async function fetchUserDataScopes(userId: number) {
  return requestClient.get(`/role/user/${userId}/data-scopes`);
}
