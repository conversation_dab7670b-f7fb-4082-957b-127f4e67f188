import psycopg2
import pandas as pd
import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.config import Config

def batch_insert_from_excel(file_path):
    """从Excel文件批量导入财务数据到PostgreSQL数据库"""
    try:
        # 连接到PostgreSQL数据库
        conn = psycopg2.connect(**Config.DATABASE_CONFIG)
        cursor = conn.cursor()

        # 验证finance表是否存在
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'finance'
            )
        """)
        table_exists = cursor.fetchone()[0]

        if not table_exists:
            raise ValueError("数据库中不存在finance表")

        print(f"成功连接到PostgreSQL数据库，找到finance表")

        # 读取Excel文件
        print("正在读取Excel文件...")
        df = pd.read_excel(file_path)

        print(f"Excel文件包含 {len(df)} 行数据")
        print(f"Excel列名: {df.columns.tolist()}")

        # 验证必要的列是否存在
        required_columns = ['shop', 'record_date', 'type', 'category', 'amount', 'description']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Excel文件缺少必要的列: {missing_columns}")

        # 数据清洗和验证
        print("正在清洗和验证数据...")

        # 处理空值
        df = df.dropna(subset=['shop', 'record_date', 'type', 'category', 'amount'])

        # 处理日期格式
        df['record_date'] = pd.to_datetime(df['record_date']).dt.strftime('%Y-%m-%d')

        # 处理时间戳字段
        if 'created_at' in df.columns:
            df['created_at'] = pd.to_datetime(df['created_at']).dt.strftime('%Y-%m-%d %H:%M:%S')
        else:
            df['created_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        if 'updated_at' in df.columns:
            df['updated_at'] = pd.to_datetime(df['updated_at']).dt.strftime('%Y-%m-%d %H:%M:%S')
        else:
            df['updated_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 处理报销相关字段
        if 'reimbursed_at' in df.columns:
            df['reimbursed_at'] = pd.to_datetime(df['reimbursed_at'], errors='coerce')
            df['reimbursed_at'] = df['reimbursed_at'].dt.strftime('%Y-%m-%d %H:%M:%S')

        # 处理可选字段的默认值
        if 'screenshot' not in df.columns:
            df['screenshot'] = ''
        if 'is_reimbursed' not in df.columns:
            df['is_reimbursed'] = 0
        if 'reimbursed_status' not in df.columns:
            df['reimbursed_status'] = 0
        if 'is_deleted' not in df.columns:
            df['is_deleted'] = 0

        # 确保数据类型正确
        df['amount'] = pd.to_numeric(df['amount'], errors='coerce')
        df['is_reimbursed'] = pd.to_numeric(df['is_reimbursed'], errors='coerce').fillna(0).astype(int)
        df['reimbursed_status'] = pd.to_numeric(df['reimbursed_status'], errors='coerce').fillna(0).astype(int)
        df['is_deleted'] = pd.to_numeric(df['is_deleted'], errors='coerce').fillna(0).astype(int)

        # 填充空的描述字段
        df['description'] = df['description'].fillna('')
        df['screenshot'] = df['screenshot'].fillna('')

        print(f"数据清洗完成，有效数据 {len(df)} 条")

        # 准备插入数据
        columns_to_insert = [
            'shop', 'record_date', 'type', 'category', 'amount', 'description',
            'created_at', 'updated_at', 'screenshot', 'is_reimbursed',
            'reimbursed_status', 'reimbursed_at', 'is_deleted'
        ]

        # 转换为记录列表
        records = []
        for _, row in df.iterrows():
            record = []
            for col in columns_to_insert:
                value = row[col]
                # 处理NaT和NaN值
                if pd.isna(value) or (isinstance(value, str) and value.lower() == 'nat'):
                    if col == 'reimbursed_at':
                        record.append(None)  # 时间戳字段用None
                    else:
                        record.append('')  # 其他字段用空字符串
                else:
                    record.append(value)
            records.append(tuple(record))

        print(f"准备插入 {len(records)} 条记录到finance表...")

        # 批量插入数据
        sql = """
        INSERT INTO finance (
            shop, record_date, type, category, amount, description,
            created_at, updated_at, screenshot, is_reimbursed,
            reimbursed_status, reimbursed_at, is_deleted
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        cursor.executemany(sql, records)

        # 重置序列计数器到正确的值，避免主键冲突
        cursor.execute("SELECT setval('finance_id_seq', (SELECT MAX(id) FROM finance))")

        conn.commit()

        print(f"✅ 成功从Excel导入 {len(records)} 条财务数据到finance表")
        print("✅ 已重置ID序列计数器，避免主键冲突")

    except psycopg2.Error as e:
        print(f"❌ PostgreSQL错误: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if conn:
            conn.close()
            print("🔒 数据库连接已关闭")

# 使用示例
if __name__ == "__main__":
    excel_file = "database/收支随机数.xlsx"  # Excel文件路径

    print("=" * 50)
    print("财务数据批量导入工具")
    print("=" * 50)

    # 检查文件是否存在
    if not os.path.exists(excel_file):
        print(f"❌ 错误：找不到Excel文件 {excel_file}")
        print("请确保文件路径正确")
        exit(1)

    print(f"📁 Excel文件路径: {excel_file}")

    # 确认导入
    confirm = input("确认要导入数据吗？(y/N): ")
    if confirm.lower() in ['y', 'yes']:
        batch_insert_from_excel(excel_file)
    else:
        print("❌ 导入已取消")