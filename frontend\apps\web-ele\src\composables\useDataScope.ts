import { ref, computed } from 'vue';
import { fetchUserDataScopes, type DataScope } from '../api/role';

// 全局数据范围状态
const userDataScopes = ref<DataScope[]>([]);
const isDataScopesLoaded = ref(false);

/**
 * 用户数据范围管理 composable
 */
export function useDataScope() {
  
  /**
   * 加载用户数据范围
   * @param userId 用户ID
   */
  const loadUserDataScopes = async (userId: number) => {
    try {
      const response = await fetchUserDataScopes(userId);

      // 处理响应格式
      // 由于requestClient配置了responseReturn: 'data'，所以response应该直接是数据数组
      let dataScopes = [];
      if (Array.isArray(response)) {
        dataScopes = response;
      } else if (response && Array.isArray(response.data)) {
        dataScopes = response.data;
      } else {
        console.warn('意外的响应格式:', response);
        dataScopes = [];
      }

      userDataScopes.value = dataScopes;
      isDataScopesLoaded.value = true;
    } catch (error) {
      console.error('加载用户数据范围失败:', error);
      userDataScopes.value = [];
      isDataScopesLoaded.value = true;
    }
  };

  /**
   * 获取用户可访问的线下店铺ID列表
   */
  const getAccessibleOfflineStoreIds = computed(() => {
    return userDataScopes.value
      .filter(scope => scope.store_type === 'offline')
      .map(scope => scope.store_id);
  });

  /**
   * 获取用户可访问的线下店铺名称列表
   */
  const getAccessibleOfflineStoreNames = computed(() => {
    return userDataScopes.value
      .filter(scope => scope.store_type === 'offline' && scope.store_name)
      .map(scope => scope.store_name!);
  });

  /**
   * 获取用户可访问的线上店铺ID列表
   */
  const getAccessibleOnlineStoreIds = computed(() => {
    return userDataScopes.value
      .filter(scope => scope.store_type === 'online')
      .map(scope => scope.store_id);
  });

  /**
   * 获取用户可访问的线上店铺名称列表
   */
  const getAccessibleOnlineStoreNames = computed(() => {
    return userDataScopes.value
      .filter(scope => scope.store_type === 'online' && scope.store_name)
      .map(scope => scope.store_name!);
  });

  /**
   * 检查用户是否可以访问指定店铺
   * @param storeId 店铺ID
   * @param storeType 店铺类型
   */
  const canAccessStore = (storeId: number, storeType: 'offline' | 'online' = 'offline') => {
    // 如果数据范围还未加载，暂时允许访问（避免阻塞）
    if (!isDataScopesLoaded.value) {
      return true;
    }

    // 如果没有配置数据范围，不允许访问任何店铺
    if (userDataScopes.value.length === 0) {
      return false;
    }

    return userDataScopes.value.some(scope =>
      scope.store_id === storeId && scope.store_type === storeType
    );
  };

  /**
   * 检查用户是否可以访问指定店铺（通过店铺名称）
   * @param storeName 店铺名称
   * @param storeType 店铺类型
   */
  const canAccessStoreByName = (storeName: string, storeType: 'offline' | 'online' = 'offline') => {
    // 如果数据范围还未加载，暂时允许访问（避免阻塞）
    if (!isDataScopesLoaded.value) {
      return true;
    }

    // 如果没有配置数据范围，不允许访问任何店铺
    if (userDataScopes.value.length === 0) {
      return false;
    }

    return userDataScopes.value.some(scope =>
      scope.store_name === storeName && scope.store_type === storeType
    );
  };

  /**
   * 过滤店铺列表，只返回用户可访问的店铺
   * @param stores 店铺列表
   * @param storeType 店铺类型
   */
  const filterAccessibleStores = <T extends { id: number; name: string }>(
    stores: T[],
    storeType: 'offline' | 'online' = 'offline'
  ): T[] => {
    // 如果数据范围还未加载，返回所有店铺（避免阻塞）
    if (!isDataScopesLoaded.value) {
      return stores;
    }

    // 如果没有配置数据范围，返回空数组
    if (userDataScopes.value.length === 0) {
      return [];
    }

    const accessibleStoreIds = storeType === 'offline'
      ? getAccessibleOfflineStoreIds.value
      : getAccessibleOnlineStoreIds.value;

    return stores.filter(store => accessibleStoreIds.includes(store.id));
  };

  /**
   * 过滤店铺选项，只返回用户可访问的店铺选项
   * @param options 店铺选项列表
   * @param storeType 店铺类型
   */
  const filterAccessibleStoreOptions = <T extends { label: string; value: string }>(
    options: T[],
    storeType: 'offline' | 'online' = 'offline'
  ): T[] => {
    // 如果数据范围还未加载，返回所有选项（避免阻塞）
    if (!isDataScopesLoaded.value) {
      return options;
    }

    // 如果没有配置数据范围，只返回"全部"选项（但实际上不会有数据）
    if (userDataScopes.value.length === 0) {
      return options.filter(option => option.value === '');
    }

    const accessibleStoreNames = storeType === 'offline'
      ? getAccessibleOfflineStoreNames.value
      : getAccessibleOnlineStoreNames.value;

    const filteredOptions = options.filter(option =>
      option.value === '' || // 保留"全部"选项
      accessibleStoreNames.includes(option.value)
    );

    return filteredOptions;
  };

  /**
   * 清除数据范围缓存
   */
  const clearDataScopes = () => {
    userDataScopes.value = [];
    isDataScopesLoaded.value = false;
  };

  return {
    // 状态
    userDataScopes: computed(() => userDataScopes.value),
    isDataScopesLoaded: computed(() => isDataScopesLoaded.value),
    
    // 计算属性
    getAccessibleOfflineStoreIds,
    getAccessibleOfflineStoreNames,
    getAccessibleOnlineStoreIds,
    getAccessibleOnlineStoreNames,
    
    // 方法
    loadUserDataScopes,
    canAccessStore,
    canAccessStoreByName,
    filterAccessibleStores,
    filterAccessibleStoreOptions,
    clearDataScopes
  };
}
