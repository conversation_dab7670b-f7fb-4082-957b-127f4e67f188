@echo off
chcp 65001 >nul
echo 开始执行数据库初始化...

:: 进入backend目录
cd backend
if %errorlevel% neq 0 (
    echo 错误：无法进入backend目录
    pause
    exit /b 1
)

:: 激活虚拟环境
call venv\Scripts\activate
if %errorlevel% neq 0 (
    echo 错误：无法激活虚拟环境
    pause
    exit /b 1
)

:: 进入database目录
cd database
if %errorlevel% neq 0 (
    echo 错误：无法进入database目录
    pause
    exit /b 1
)

:: 执行初始化脚本
echo 正在执行数据库初始化脚本...
python init_database.py
if %errorlevel% neq 0 (
    echo 错误：执行init_database.py失败
    pause
    exit /b 1
)

echo 数据库初始化完成！
pause
    