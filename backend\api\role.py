from flask import Blueprint, request, g
from functools import wraps
from models.role import Role
from models.permission import Permission
from utils.response import ResponseUtils
from utils.jwt_utils import JWTUtils
from utils.datetime_utils import format_datetime_list

# 创建角色管理蓝图
role_bp = Blueprint('role', __name__, url_prefix='/api/role')

def token_required(f):
    """JWT令牌验证装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        
        # 从请求头获取令牌
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            try:
                token = auth_header.split(" ")[1]  # Bearer <token>
            except IndexError:
                return ResponseUtils.unauthorized("令牌格式错误")
        
        if not token:
            return ResponseUtils.unauthorized("缺少访问令牌")
        
        # 验证令牌
        payload = JWTUtils.verify_access_token(token)
        if not payload:
            return ResponseUtils.unauthorized("无效的访问令牌")
        
        # 将用户信息存储到g对象中
        g.current_user = payload
        return f(*args, **kwargs)
    
    return decorated

@role_bp.route('/list', methods=['GET'])
@token_required
def get_roles_list():
    """获取角色列表接口"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        name = request.args.get('name', '').strip()
        status = request.args.get('status', '').strip()

        # 处理状态参数
        status_value = None
        if status == '1' or status == 'true':
            status_value = 1
        elif status == '0' or status == 'false':
            status_value = 0

        # 处理空字符串
        name = name if name else None

        role_model = Role()
        result = role_model.get_roles_list(
            page=page,
            page_size=page_size,
            name=name,
            status=status_value
        )

        # 格式化时间字段
        result['roles'] = format_datetime_list(result['roles'], ['created_at', 'updated_at'])

        # 格式化返回数据以符合前端期望的格式
        response_data = {
            'data': result['roles'],  # 前端期望data字段包含角色数组
            'total': result['total'],
            'page': result['page'],
            'page_size': result['page_size']
        }

        return ResponseUtils.success(response_data, "获取角色列表成功")

    except Exception as e:
        print(f"获取角色列表错误: {str(e)}")
        return ResponseUtils.error("获取角色列表失败", 500)

@role_bp.route('/create', methods=['POST'])
@token_required
def create_role():
    """创建角色接口"""
    try:
        data = request.get_json()

        # 验证必填字段
        if not data.get('name'):
            return ResponseUtils.bad_request("角色名不能为空")

        name = data['name'].strip()
        description = data.get('description', '').strip() or None
        status = int(data.get('status', 1))

        # 验证数据格式
        if len(name) < 2:
            return ResponseUtils.bad_request("角色名至少2个字符")

        role_model = Role()
        result = role_model.create_role(
            name=name,
            description=description,
            status=status
        )

        if result['success']:
            return ResponseUtils.success({'role_id': result['role_id']}, result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])

    except Exception as e:
        print(f"创建角色错误: {str(e)}")
        return ResponseUtils.error("创建角色失败", 500)

@role_bp.route('/update/<int:role_id>', methods=['PUT'])
@token_required
def update_role(role_id):
    """更新角色接口"""
    try:
        data = request.get_json()

        name = data.get('name', '').strip() or None
        description = data.get('description', '').strip() or None
        status = data.get('status')

        # 验证数据格式
        if name and len(name) < 2:
            return ResponseUtils.bad_request("角色名至少2个字符")

        if status is not None:
            status = int(status)

        role_model = Role()
        result = role_model.update_role(
            role_id=role_id,
            name=name,
            description=description,
            status=status
        )

        if result['success']:
            return ResponseUtils.success(None, result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])

    except Exception as e:
        print(f"更新角色错误: {str(e)}")
        return ResponseUtils.error("更新角色失败", 500)

@role_bp.route('/delete/<int:role_id>', methods=['DELETE'])
@token_required
def delete_role(role_id):
    """删除角色接口"""
    try:
        role_model = Role()
        result = role_model.delete_role(role_id)

        if result['success']:
            return ResponseUtils.success(None, result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])

    except Exception as e:
        print(f"删除角色错误: {str(e)}")
        return ResponseUtils.error("删除角色失败", 500)

@role_bp.route('/batch-delete', methods=['POST'])
@token_required
def batch_delete_roles():
    """批量删除角色接口"""
    try:
        data = request.get_json()
        role_ids = data.get('ids', [])

        if not role_ids:
            return ResponseUtils.bad_request("请选择要删除的角色")

        role_model = Role()
        result = role_model.batch_delete_roles(role_ids)

        if result['success']:
            return ResponseUtils.success(result.get('failed_details'), result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])

    except Exception as e:
        print(f"批量删除角色错误: {str(e)}")
        return ResponseUtils.error("批量删除角色失败", 500)

@role_bp.route('/status/<int:role_id>', methods=['PUT'])
@token_required
def update_role_status(role_id):
    """更新角色状态接口"""
    try:
        data = request.get_json()
        status = data.get('status')

        if status is None:
            return ResponseUtils.bad_request("状态参数不能为空")

        status = int(status)

        role_model = Role()
        result = role_model.update_role(role_id=role_id, status=status)

        if result['success']:
            return ResponseUtils.success(None, result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])

    except Exception as e:
        print(f"更新角色状态错误: {str(e)}")
        return ResponseUtils.error("更新角色状态失败", 500)

@role_bp.route('/all', methods=['GET'])
@token_required
def get_all_roles():
    """获取所有启用的角色接口（用于下拉选择）"""
    try:
        role_model = Role()
        roles = role_model.get_all_roles()

        return ResponseUtils.success(roles, "获取角色列表成功")

    except Exception as e:
        print(f"获取角色列表错误: {str(e)}")
        return ResponseUtils.error("获取角色列表失败", 500)

@role_bp.route('/<int:role_id>/permissions', methods=['GET'])
@token_required
def get_role_permissions(role_id):
    """获取角色权限接口"""
    try:
        permission_model = Permission()
        permissions = permission_model.get_role_permissions(role_id)

        return ResponseUtils.success(permissions, "获取角色权限成功")

    except Exception as e:
        print(f"获取角色权限错误: {str(e)}")
        return ResponseUtils.error("获取角色权限失败", 500)

@role_bp.route('/<int:role_id>/permissions', methods=['POST'])
@token_required
def assign_role_permissions(role_id):
    """分配角色权限接口"""
    try:
        data = request.get_json()
        permission_ids = data.get('permission_ids', [])

        permission_model = Permission()
        result = permission_model.assign_permissions_to_role(role_id, permission_ids)

        if result['success']:
            return ResponseUtils.success(None, result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])

    except Exception as e:
        print(f"分配角色权限错误: {str(e)}")
        return ResponseUtils.error("分配角色权限失败", 500)

@role_bp.route('/<int:role_id>/data-scopes', methods=['GET'])
@token_required
def get_role_data_scopes(role_id):
    """获取角色数据范围接口"""
    try:
        role_model = Role()
        scopes = role_model.get_role_data_scopes(role_id)

        return ResponseUtils.success(scopes, "获取角色数据范围成功")

    except Exception as e:
        print(f"获取角色数据范围错误: {str(e)}")
        return ResponseUtils.error("获取角色数据范围失败", 500)

@role_bp.route('/<int:role_id>/data-scopes', methods=['POST'])
@token_required
def set_role_data_scopes(role_id):
    """设置角色数据范围接口"""
    try:
        data = request.get_json()
        store_scopes = data.get('store_scopes', [])

        role_model = Role()
        result = role_model.set_role_data_scopes(role_id, store_scopes)

        if result['success']:
            return ResponseUtils.success(None, result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])

    except Exception as e:
        print(f"设置角色数据范围错误: {str(e)}")
        return ResponseUtils.error("设置角色数据范围失败", 500)

@role_bp.route('/user/<int:user_id>/data-scopes', methods=['GET'])
@token_required
def get_user_data_scopes(user_id):
    """获取用户数据范围接口"""
    try:
        role_model = Role()
        scopes = role_model.get_user_data_scopes(user_id)

        return ResponseUtils.success(scopes, "获取用户数据范围成功")

    except Exception as e:
        print(f"获取用户数据范围错误: {str(e)}")
        return ResponseUtils.error("获取用户数据范围失败", 500)
