from flask import Blueprint, request, jsonify
from .utils import get_db_connection, format_response, validate_required_fields
from .auth import token_required, check_permission
from utils.datetime_utils import format_datetime_list
from models.role import Role


financepayment_bp = Blueprint('financepayment', __name__, url_prefix='/api')

@financepayment_bp.route('/financepayment', methods=['GET'])
@token_required
@check_permission('MENU_FINANCEPAYMENT')
def get_financepayment_list():
    """获取回款登记列表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取当前用户ID
        from flask import g
        current_user_id = g.current_user.get('user_id')

        # 获取用户数据范围
        role_model = Role()
        user_data_scopes = role_model.get_user_data_scopes(current_user_id)
        accessible_store_names = [scope['store_name'] for scope in user_data_scopes if scope['store_type'] == 'offline' and scope['store_name']]

        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        date = request.args.get('date')
        shop = request.args.get('shop')
        category = request.args.get('category')
        
        # 构建查询条件
        where_conditions = ['is_deleted = 0']
        params = []
        
        # 日期条件
        if date:
            where_conditions.append('record_date = %s')
            params.append(date)
        elif start_date and end_date:
            where_conditions.append('record_date >= %s AND record_date <= %s')
            params.extend([start_date, end_date])
        
        # 店铺条件
        if shop:
            where_conditions.append('shop LIKE %s')
            params.append(f'%{shop}%')
        
        # 分类条件
        if category:
            where_conditions.append('category LIKE %s')
            params.append(f'%{category}%')

        # 数据范围过滤：如果用户有数据范围限制，只显示可访问店铺的数据
        if accessible_store_names:
            placeholders = ','.join(['%s'] * len(accessible_store_names))
            where_conditions.append(f'shop IN ({placeholders})')
            params.extend(accessible_store_names)
        elif user_data_scopes is not None and len(user_data_scopes) == 0:
            # 如果用户有角色但没有数据范围，不显示任何数据
            where_conditions.append('1 = 0')  # 永远为假的条件

        # 构建WHERE子句
        where_clause = ' AND '.join(where_conditions)
        
        # 获取总数
        count_query = f'SELECT COUNT(*) FROM financepayment WHERE {where_clause}'
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]
        
        # 获取分页数据
        offset = (page - 1) * page_size
        data_query = f'''
            SELECT id, shop, record_date, category, amount, created_at, updated_at, screenshot
            FROM financepayment 
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
        '''
        cursor.execute(data_query, params + [page_size, offset])
        
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        
        # 转换为字典列表
        results = []
        for row in cursor.fetchall():
            row_dict = dict(zip(columns, row))
            results.append(row_dict)
        
        # 格式化时间字段
        results = format_datetime_list(results, ['created_at', 'updated_at'])
        
        return format_response(
            success=True,
            data=results,
            total=total,
            page=page,
            pageSize=page_size
        )
        
    except Exception as e:
        import traceback
        print(f"获取回款登记列表错误: {str(e)}")
        print(f"错误详情: {traceback.format_exc()}")
        return format_response(success=False, message=f"获取回款登记列表失败: {str(e)}")
    finally:
        conn.close()

@financepayment_bp.route('/financepayment', methods=['POST'])
@token_required
@check_permission('FINANCEPAYMENT_ADD')
def create_financepayment():
    """创建回款登记记录"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['shop', 'record_date', 'category', 'amount']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return validation_error

        conn = get_db_connection()
        cursor = conn.cursor()

        # 图片路径直接使用前端传来的路径（已通过upload API上传）
        screenshot_path = data.get('screenshot', '')

        # 插入回款登记记录
        from datetime import datetime
        now = datetime.now().isoformat()
        cursor.execute('''
            INSERT INTO financepayment (shop, record_date, category, amount, screenshot, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            RETURNING id
        ''', (
            data['shop'],
            data['record_date'],
            data['category'],
            data.get('amount', 0),
            screenshot_path,
            now,
            now
        ))

        financepayment_id = cursor.fetchone()[0]
        conn.commit()

        return format_response(
            success=True,
            data={'id': financepayment_id, **data},
            message="回款登记记录创建成功"
        )

    except Exception as e:
        print(f"创建回款登记记录错误: {str(e)}")
        return format_response(success=False, message="创建回款登记记录失败")
    finally:
        conn.close()

@financepayment_bp.route('/financepayment/<int:financepayment_id>', methods=['PUT'])
@token_required
@check_permission('FINANCEPAYMENT_EDIT')
def update_financepayment(financepayment_id):
    """更新回款登记记录"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['shop', 'record_date', 'category', 'amount']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return validation_error

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查回款登记记录是否存在
        cursor.execute('SELECT COUNT(*) FROM financepayment WHERE id = %s AND is_deleted = 0', (financepayment_id,))
        if cursor.fetchone()[0] == 0:
            return format_response(success=False, message="回款登记记录不存在")

        # 图片路径直接使用前端传来的路径（已通过upload API上传）
        screenshot_path = data.get('screenshot', '')

        # 更新回款登记记录
        from datetime import datetime
        now = datetime.now().isoformat()
        cursor.execute('''
            UPDATE financepayment
            SET shop = %s, record_date = %s, category = %s, amount = %s,
                screenshot = %s, updated_at = %s
            WHERE id = %s
        ''', (
            data['shop'],
            data['record_date'],
            data['category'],
            data.get('amount', 0),
            screenshot_path,
            now,
            financepayment_id
        ))

        conn.commit()

        return format_response(
            success=True,
            data={'id': financepayment_id, **data},
            message="回款登记记录更新成功"
        )

    except Exception as e:
        print(f"更新回款登记记录错误: {str(e)}")
        return format_response(success=False, message="更新回款登记记录失败")
    finally:
        conn.close()

@financepayment_bp.route('/financepayment/<int:financepayment_id>', methods=['DELETE'])
@token_required
@check_permission('FINANCEPAYMENT_DELETE')
def delete_financepayment(financepayment_id):
    """删除回款登记记录"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查回款登记记录是否存在
        cursor.execute('SELECT COUNT(*) FROM financepayment WHERE id = %s AND is_deleted = 0', (financepayment_id,))
        if cursor.fetchone()[0] == 0:
            return format_response(success=False, message="回款登记记录不存在")

        # 软删除
        cursor.execute('UPDATE financepayment SET is_deleted = 1 WHERE id = %s', (financepayment_id,))
        conn.commit()

        return format_response(success=True, message="删除成功")

    except Exception as e:
        print(f"删除回款登记记录错误: {str(e)}")
        return format_response(success=False, message="删除失败")
    finally:
        conn.close()

@financepayment_bp.route('/financepayment/batch-delete', methods=['POST'])
@token_required
@check_permission('FINANCEPAYMENT_BATCH_DELETE')
def batch_delete_financepayment():
    """批量删除回款登记记录"""
    try:
        data = request.get_json()
        ids = data.get('ids', [])

        if not ids:
            return format_response(success=False, message="请提供要删除的回款登记记录ID")

        conn = get_db_connection()
        cursor = conn.cursor()

        # 批量软删除
        placeholders = ','.join(['%s' for _ in ids])
        cursor.execute(f'UPDATE financepayment SET is_deleted = 1 WHERE id IN ({placeholders})', ids)
        conn.commit()

        return format_response(success=True, message="批量删除成功")

    except Exception as e:
        print(f"批量删除回款登记记录错误: {str(e)}")
        return format_response(success=False, message="批量删除失败")
    finally:
        conn.close()


@financepayment_bp.route('/financepayment/batch-import', methods=['POST'])
@token_required
@check_permission('FINANCEPAYMENT_BATCH_ADD')
def batch_import_financepayment():
    """批量导入回款登记记录"""
    try:
        # 检查是否有文件上传
        if 'file' not in request.files:
            return format_response(success=False, message="请选择要导入的Excel文件")

        file = request.files['file']
        if file.filename == '':
            return format_response(success=False, message="请选择要导入的Excel文件")

        # 检查文件类型
        if not file.filename.lower().endswith(('.xlsx', '.xls')):
            return format_response(success=False, message="请上传Excel文件（.xlsx或.xls格式）")

        # 导入pandas用于处理Excel文件
        import pandas as pd
        import io
        from datetime import datetime

        # 读取Excel文件
        try:
            # 将文件内容读取到内存中
            file_content = file.read()
            df = pd.read_excel(io.BytesIO(file_content), sheet_name=0)  # 读取第一个工作表
        except Exception as e:
            return format_response(success=False, message=f"Excel文件读取失败: {str(e)}")

        # 检查Excel是否为空
        if df.empty:
            return format_response(success=False, message="Excel文件为空")

        # 确保列名匹配（忽略大小写和空格）
        df.columns = df.columns.str.strip().str.lower()

        # 定义必需的列映射
        required_columns = {
            '店铺': 'shop',
            '日期': 'record_date',
            '分类': 'category',
            '金额': 'amount'
        }

        # 检查必需的列是否存在
        missing_columns = []
        for chinese_col, english_col in required_columns.items():
            if chinese_col.lower() not in df.columns:
                missing_columns.append(chinese_col)

        if missing_columns:
            return format_response(
                success=False,
                message=f"Excel文件缺少必需的列: {', '.join(missing_columns)}"
            )

        # 重命名列
        column_rename_map = {chinese_col.lower(): english_col for chinese_col, english_col in required_columns.items()}
        df = df.rename(columns=column_rename_map)

        # 数据验证和清理
        errors = []
        valid_records = []

        for index, row in df.iterrows():
            row_errors = []

            # 验证必填字段
            if pd.isna(row['shop']) or str(row['shop']).strip() == '':
                row_errors.append(f"第{index+2}行：店铺不能为空")

            if pd.isna(row['record_date']):
                row_errors.append(f"第{index+2}行：日期不能为空")

            if pd.isna(row['category']) or str(row['category']).strip() == '':
                row_errors.append(f"第{index+2}行：分类不能为空")

            if pd.isna(row['amount']):
                row_errors.append(f"第{index+2}行：金额不能为空")
            else:
                try:
                    amount = float(row['amount'])
                    if amount <= 0:
                        row_errors.append(f"第{index+2}行：金额必须大于0")
                except (ValueError, TypeError):
                    row_errors.append(f"第{index+2}行：金额格式不正确")

            # 处理日期格式
            try:
                if pd.isna(row['record_date']):
                    row_errors.append(f"第{index+2}行：日期不能为空")
                else:
                    # 尝试解析日期
                    if isinstance(row['record_date'], str):
                        # 如果是字符串，尝试解析
                        date_obj = pd.to_datetime(row['record_date'])
                    else:
                        # 如果是datetime对象，直接使用
                        date_obj = row['record_date']

                    # 格式化为YYYY-MM-DD字符串
                    formatted_date = date_obj.strftime('%Y-%m-%d')
                    row['record_date'] = formatted_date
            except Exception as e:
                row_errors.append(f"第{index+2}行：日期格式不正确")

            if row_errors:
                errors.extend(row_errors)
            else:
                # 添加到有效记录列表
                valid_records.append({
                    'shop': str(row['shop']).strip(),
                    'record_date': row['record_date'],
                    'category': str(row['category']).strip(),
                    'amount': float(row['amount']),
                    'screenshot': ''  # 批量导入时截图为空
                })

        # 如果有验证错误，返回错误信息
        if errors:
            return format_response(
                success=False,
                message="数据验证失败",
                data={
                    'total': len(df),
                    'success': 0,
                    'failed': len(df),
                    'errors': errors[:10]  # 只返回前10个错误
                }
            )

        # 批量插入数据库
        conn = get_db_connection()
        cursor = conn.cursor()

        success_count = 0
        failed_count = 0
        now = datetime.now().isoformat()

        for record in valid_records:
            try:
                cursor.execute('''
                    INSERT INTO financepayment (shop, record_date, category, amount, screenshot, created_at, updated_at, is_deleted)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ''', (
                    record['shop'],
                    record['record_date'],
                    record['category'],
                    record['amount'],
                    record['screenshot'],
                    now,
                    now,
                    0
                ))
                success_count += 1
            except Exception as e:
                failed_count += 1
                print(f"插入记录失败: {str(e)}")

        conn.commit()

        return format_response(
            success=True,
            message=f"批量导入完成，成功导入{success_count}条记录",
            data={
                'total': len(valid_records),
                'success': success_count,
                'failed': failed_count
            }
        )

    except Exception as e:
        print(f"批量导入回款登记记录错误: {str(e)}")
        return format_response(success=False, message=f"批量导入失败: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()
