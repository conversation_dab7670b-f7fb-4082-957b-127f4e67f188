<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElDialog, ElUpload, ElButton, ElMessage, ElAlert, ElIcon } from 'element-plus';
import { createIconifyIcon } from '@vben/icons';

// 创建图标组件
const UploadIcon = createIconifyIcon('mdi:upload');
const DownloadIcon = createIconifyIcon('mdi:download');

interface Props {
  visible: boolean;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<{
  'update:visible': [value: boolean];
  'import': [file: File];
}>();

// 本地状态
const dialogVisible = ref(false);
const uploadRef = ref();
const selectedFile = ref<File | null>(null);

// 监听外部visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
});

// 监听内部dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal);
  if (!newVal) {
    // 关闭时清理状态
    selectedFile.value = null;
    uploadRef.value?.clearFiles();
  }
});

// 文件选择处理
function handleFileChange(file: any) {
  selectedFile.value = file.raw;
  return false; // 阻止自动上传
}

// 文件移除处理
function handleFileRemove() {
  selectedFile.value = null;
}

// 执行导入
function handleImport() {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择要导入的Excel文件');
    return;
  }
  
  emit('import', selectedFile.value);
}

// 下载模板
function downloadTemplate() {
  // 创建一个简单的Excel模板数据
  const templateData = [
    ['店铺', '日期', '分类', '金额'],
    ['示例店铺', '2024-01-01', '示例分类', '100.00'],
    ['', '', '', '']
  ];
  
  // 创建CSV格式的模板（简化版本，实际项目中可能需要使用专门的Excel库）
  const csvContent = templateData.map(row => row.join(',')).join('\n');
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  
  link.setAttribute('href', url);
  link.setAttribute('download', '回款登记导入模板.csv');
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  ElMessage.success('模板下载成功');
}

// 文件上传前的检查
function beforeUpload(file: File) {
  const isExcel = file.name.toLowerCase().endsWith('.xlsx') || file.name.toLowerCase().endsWith('.xls');
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件（.xlsx或.xls格式）');
    return false;
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB');
    return false;
  }
  
  return true;
}
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="批量导入回款登记"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="batch-import-content">
      <!-- 说明信息 -->
      <ElAlert
        title="导入说明"
        type="info"
        :closable="false"
        show-icon
        class="mb-4"
      >
        <template #default>
          <div class="text-sm">
            <p>1. 请下载模板文件，按照模板格式填写数据</p>
            <p>2. 必填字段：店铺、日期、分类、金额</p>
            <p>3. 日期格式：YYYY-MM-DD（如：2024-01-01）</p>
            <p>4. 金额必须为正数</p>
            <p>5. 支持.xlsx和.xls格式的Excel文件</p>
          </div>
        </template>
      </ElAlert>

      <!-- 模板下载 -->
      <div class="mb-4">
        <ElButton 
          type="primary" 
          plain 
          @click="downloadTemplate"
        >
          <template #icon>
            <DownloadIcon />
          </template>
          下载导入模板
        </ElButton>
      </div>

      <!-- 文件上传 -->
      <div class="upload-area">
        <ElUpload
          ref="uploadRef"
          :auto-upload="false"
          :show-file-list="true"
          :limit="1"
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          accept=".xlsx,.xls"
          drag
        >
          <div class="upload-content">
            <ElIcon class="upload-icon">
              <UploadIcon />
            </ElIcon>
            <div class="upload-text">
              <p>将Excel文件拖拽到此处，或<em>点击选择文件</em></p>
              <p class="upload-tip">支持.xlsx和.xls格式，文件大小不超过10MB</p>
            </div>
          </div>
        </ElUpload>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">取消</ElButton>
        <ElButton 
          type="primary" 
          :loading="props.loading"
          :disabled="!selectedFile"
          @click="handleImport"
        >
          开始导入
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.batch-import-content {
  padding: 0;
}

.upload-area {
  margin-top: 16px;
}

.upload-content {
  text-align: center;
  padding: 40px 20px;
}

.upload-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 16px;
}

.upload-text p {
  margin: 8px 0;
  color: #606266;
}

.upload-text em {
  color: #409eff;
  font-style: normal;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-upload-dragger) {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  transition: all 0.3s;
}

:deep(.el-upload-dragger:hover) {
  border-color: #409eff;
  background-color: #f5f7fa;
}

:deep(.el-upload-dragger.is-dragover) {
  border-color: #409eff;
  background-color: #f0f9ff;
}
</style>
