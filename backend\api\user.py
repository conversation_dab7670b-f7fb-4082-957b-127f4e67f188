from flask import Blueprint, request, g
from models.user import User
from models.role import Role
from utils.jwt_utils import JWTUtils
from utils.datetime_utils import format_datetime_list
from utils.response import ResponseUtils
from functools import wraps

user_bp = Blueprint('user', __name__, url_prefix='/api/user')

def token_required(f):
    """JWT令牌验证装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        
        # 从请求头获取令牌
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            try:
                token = auth_header.split(" ")[1]  # Bearer <token>
            except IndexError:
                return ResponseUtils.unauthorized("令牌格式错误")
        
        if not token:
            return ResponseUtils.unauthorized("缺少访问令牌")
        
        # 验证令牌
        payload = JWTUtils.verify_access_token(token)
        if not payload:
            return ResponseUtils.unauthorized("无效的访问令牌")
        
        # 将用户信息存储到g对象中
        g.current_user = payload
        return f(*args, **kwargs)
    
    return decorated

@user_bp.route('/info', methods=['GET'])
@token_required
def get_user_info():
    """获取用户信息接口"""
    try:
        user_id = g.current_user['user_id']

        # 从数据库获取最新的用户信息
        user_model = User()
        user = user_model.get_user_by_id(user_id)

        if not user:
            return ResponseUtils.not_found("用户不存在")

        # 获取用户的角色信息
        user_roles = user_model.get_user_role_names(user['id'])

        # 如果没有分配角色，使用旧的role字段作为兼容
        if not user_roles and user['role']:
            user_roles = [user['role']]

        # 获取用户的数据范围
        from models.role import Role
        role_model = Role()
        user_data_scopes = role_model.get_user_data_scopes(user['id'])

        # 准备返回的用户信息
        user_info = {
            'id': user['id'],
            'userId': user['id'],  # 添加userId字段以确保兼容性
            'username': user['username'],
            'phone': user['phone'],
            'role': user['role'],  # 保持兼容性
            'realName': user['username'],  # 前端需要的字段
            'roles': user_roles,  # 前端需要的字段格式
            'status': user['status'],
            'last_login': user['last_login'],
            'created_at': user['created_at'],
            'data_scopes': user_data_scopes  # 用户数据范围
        }

        return ResponseUtils.success(user_info, "获取用户信息成功")

    except Exception as e:
        print(f"获取用户信息错误: {str(e)}")
        return ResponseUtils.error("获取用户信息失败", 500)

@user_bp.route('/list', methods=['GET'])
@token_required
def get_users_list():
    """获取用户列表接口"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        username = request.args.get('username', '').strip()
        phone = request.args.get('phone', '').strip()
        status = request.args.get('status', '').strip()

        # 处理状态参数
        status_value = None
        if status == '1' or status == 'true':
            status_value = 1
        elif status == '0' or status == 'false':
            status_value = 0

        # 处理空字符串
        username = username if username else None
        phone = phone if phone else None

        user_model = User()
        result = user_model.get_users_list(
            page=page,
            page_size=page_size,
            username=username,
            phone=phone,
            status=status_value
        )

        # 格式化时间字段
        result['users'] = format_datetime_list(result['users'], ['last_login', 'created_at', 'updated_at'])

        # 格式化返回数据
        for user in result['users']:
            # 格式化状态显示
            user['statusText'] = '启用' if user['status'] == 1 else '禁用'

            # 格式化角色显示 - 使用role_names而不是role字段
            if 'role_names' in user and user['role_names']:
                user['role'] = user['role_names']  # 前端期望role字段是角色名称数组
            else:
                user['role'] = []

        # 格式化返回数据以符合前端期望的格式
        response_data = {
            'data': result['users'],  # 前端期望data字段包含用户数组
            'total': result['total'],
            'page': result['page'],
            'page_size': result['page_size']
        }

        return ResponseUtils.success(response_data, "获取用户列表成功")

    except Exception as e:
        print(f"获取用户列表错误: {str(e)}")
        return ResponseUtils.error("获取用户列表失败", 500)

@user_bp.route('/create', methods=['POST'])
@token_required
def create_user():
    """创建用户接口"""
    try:
        data = request.get_json()

        # 验证必填字段
        if not data.get('username'):
            return ResponseUtils.bad_request("用户名不能为空")

        if not data.get('password'):
            return ResponseUtils.bad_request("密码不能为空")

        username = data['username'].strip()
        password = data['password'].strip()
        phone = data.get('phone', '').strip() or None
        roles = data.get('roles', ['3'])  # 默认为普通用户角色ID
        status = int(data.get('status', 1))

        # 验证数据格式
        if len(username) < 3:
            return ResponseUtils.bad_request("用户名至少3个字符")

        if len(password) < 6:
            return ResponseUtils.bad_request("密码至少6个字符")

        if phone and len(phone) != 11:
            return ResponseUtils.bad_request("手机号格式不正确")

        if not roles or not isinstance(roles, list):
            return ResponseUtils.bad_request("角色不能为空")

        user_model = User()
        result = user_model.create_user_with_roles(
            username=username,
            password=password,
            phone=phone,
            role_ids=roles,
            status=status
        )

        if result['success']:
            return ResponseUtils.success({'id': result['user_id']}, result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])

    except Exception as e:
        print(f"创建用户错误: {str(e)}")
        return ResponseUtils.error(f"创建用户失败: {str(e)}", 500)

@user_bp.route('/update/<int:user_id>', methods=['PUT'])
@token_required
def update_user(user_id):
    """更新用户接口"""
    try:
        data = request.get_json()

        username = data.get('username', '').strip() or None
        phone = data.get('phone', '').strip() or None
        roles = data.get('roles', None)
        status = data.get('status')

        # 验证数据格式
        if username and len(username) < 3:
            return ResponseUtils.bad_request("用户名至少3个字符")

        if phone and len(phone) != 11:
            return ResponseUtils.bad_request("手机号格式不正确")

        if status is not None:
            status = int(status)

        user_model = User()
        result = user_model.update_user_with_roles(
            user_id=user_id,
            username=username,
            phone=phone,
            role_ids=roles,
            status=status
        )

        if result['success']:
            return ResponseUtils.success(None, result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])

    except Exception as e:
        print(f"更新用户错误: {str(e)}")
        return ResponseUtils.error("更新用户失败", 500)

@user_bp.route('/delete/<int:user_id>', methods=['DELETE'])
@token_required
def delete_user(user_id):
    """删除用户接口"""
    try:
        current_user_id = g.current_user['user_id']
        user_model = User()
        result = user_model.delete_user(user_id, current_user_id)

        if result['success']:
            return ResponseUtils.success(None, result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])

    except Exception as e:
        print(f"删除用户错误: {str(e)}")
        return ResponseUtils.error("删除用户失败", 500)

@user_bp.route('/batch-delete', methods=['POST'])
@token_required
def batch_delete_users():
    """批量删除用户接口"""
    try:
        data = request.get_json()
        user_ids = data.get('userIds', [])

        if not user_ids:
            return ResponseUtils.bad_request("请选择要删除的用户")

        current_user_id = g.current_user['user_id']
        user_model = User()
        result = user_model.batch_delete_users(user_ids, current_user_id)

        if result['success']:
            return ResponseUtils.success(None, result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])

    except Exception as e:
        print(f"批量删除用户错误: {str(e)}")
        return ResponseUtils.error("批量删除用户失败", 500)

@user_bp.route('/status/<int:user_id>', methods=['PUT'])
@token_required
def update_user_status(user_id):
    """更新用户状态接口"""
    try:
        data = request.get_json()
        status = data.get('status')

        if status is None:
            return ResponseUtils.bad_request("状态参数不能为空")

        # 检查是否修改自己的状态
        current_user_id = g.current_user['user_id']
        if user_id == current_user_id:
            return ResponseUtils.bad_request("不能修改自己的状态")

        status = int(status)

        user_model = User()
        result = user_model.update_user_status(user_id, status)

        if result['success']:
            return ResponseUtils.success(None, result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])

    except Exception as e:
        print(f"更新用户状态错误: {str(e)}")
        return ResponseUtils.error("更新用户状态失败", 500)

@user_bp.route('/<int:user_id>/roles', methods=['GET'])
@token_required
def get_user_roles(user_id):
    """获取用户角色接口"""
    try:
        user_model = User()
        roles = user_model.get_user_roles(user_id)

        return ResponseUtils.success(roles, "获取用户角色成功")

    except Exception as e:
        print(f"获取用户角色错误: {str(e)}")
        return ResponseUtils.error("获取用户角色失败", 500)

@user_bp.route('/<int:user_id>/roles', methods=['POST'])
@token_required
def assign_user_roles(user_id):
    """分配用户角色接口"""
    try:
        data = request.get_json()
        role_ids = data.get('role_ids', [])

        user_model = User()
        result = user_model.assign_roles_to_user(user_id, role_ids)

        if result['success']:
            return ResponseUtils.success(None, result['message'])
        else:
            return ResponseUtils.bad_request(result['message'])

    except Exception as e:
        print(f"分配用户角色错误: {str(e)}")
        return ResponseUtils.error("分配用户角色失败", 500)

@user_bp.route('/roles/all', methods=['GET'])
@token_required
def get_all_roles_for_user():
    """获取所有可用角色接口（用于用户角色分配）"""
    try:
        role_model = Role()
        roles = role_model.get_all_roles()

        return ResponseUtils.success(roles, "获取角色列表成功")

    except Exception as e:
        print(f"获取角色列表错误: {str(e)}")
        return ResponseUtils.error("获取角色列表失败", 500)
